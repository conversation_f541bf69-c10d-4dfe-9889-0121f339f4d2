import React, { createContext, useContext, ReactNode } from 'react';
import { useDayCycleTimer } from '../Hooks/useDayCycleTimer';
import type { DayPhase, CycleStatus, DayCycleState } from '../Hooks/useDayCycleTimer';

// 🌅 CONTEXTE GLOBAL POUR LE CYCLE DE JOURNÉE
// Partage l'état du cycle entre tous les composants pour une synchronisation parfaite

export interface DayCycleContextType {
  // 📊 État actuel
  currentPhase: DayPhase;
  phaseIndex: number;
  progress: number;
  phaseProgress: number;
  elapsedTime: number;
  cycleDuration: number;
  status: CycleStatus;
  lastUpdateTime: number;
  
  // 🎨 Utilitaires
  currentPhaseEmoji: string;
  phaseName: DayPhase;
  phaseNumber: number;
  totalPhases: number;
  phaseDuration: number;
  
  // 🎮 Contrôles principaux
  start: () => void;
  pause: () => void;
  resume: () => void;
  stop: () => void;
  reset: () => void;
  setCycleDuration: (durationMinutes: number) => void;
  
  // 🔧 CISCO: Contrôles de navigation
  goToPhase: (phase: DayPhase) => void;
  goToPreviousPhase: () => void;
  goToNextPhase: () => void;
  setPhaseProgress: (progress: number) => void; // 🎛️ CISCO: Contrôle manuel de la progression
  
  // 📋 Données de référence
  allPhases: DayPhase[];
  phaseEmojis: Record<DayPhase, string>;
  
  // 🔧 État technique
  isRunning: boolean;
  isPaused: boolean;
  isStopped: boolean;
  canGoPrevious: boolean;
  canGoNext: boolean;
  previousPhase: DayPhase;
  nextPhase: DayPhase;
  
  // 🎛️ CISCO: Contrôle manuel
  isManualMode: boolean;
  manualPhaseProgress: number | null;

  // 🔧 Persistance
  persistenceEnabled: boolean;
  persistenceKey: string;
}

// 🔧 CISCO: Contexte avec valeur par défaut
const DayCycleContext = createContext<DayCycleContextType | null>(null);

// 🔧 CISCO: Props du provider
export interface DayCycleProviderProps {
  children: ReactNode;
  initialCycleDuration?: number; // En minutes
  autoRestart?: boolean;
  initialPhase?: DayPhase;
  enablePersistence?: boolean;
  persistenceKey?: string;
  onPhaseChange?: (phase: DayPhase, phaseIndex: number) => void;
  onCycleComplete?: () => void;
  onSoundChange?: (phase: DayPhase) => void;
}

// 🌅 PROVIDER DU CONTEXTE
export const DayCycleProvider: React.FC<DayCycleProviderProps> = ({
  children,
  initialCycleDuration = 8,
  autoRestart = true,
  initialPhase = 'dawn',
  enablePersistence = true,
  persistenceKey = 'dayCycleTimer_global',
  onPhaseChange,
  onCycleComplete,
  onSoundChange
}) => {
  // 🔧 CISCO: Utiliser le hook amélioré
  const dayCycleData = useDayCycleTimer({
    initialCycleDuration,
    autoRestart,
    initialPhase,
    enablePersistence,
    persistenceKey,
    onPhaseChange,
    onCycleComplete,
    onSoundChange
  });

  return (
    <DayCycleContext.Provider value={dayCycleData}>
      {children}
    </DayCycleContext.Provider>
  );
};

// 🔧 CISCO: Hook pour utiliser le contexte
export const useDayCycle = (): DayCycleContextType => {
  const context = useContext(DayCycleContext);
  
  if (!context) {
    throw new Error('useDayCycle doit être utilisé à l\'intérieur d\'un DayCycleProvider');
  }
  
  return context;
};

// 🔧 CISCO: Hook optionnel (retourne null si pas de provider)
export const useDayCycleOptional = (): DayCycleContextType | null => {
  return useContext(DayCycleContext);
};

export default DayCycleContext;
