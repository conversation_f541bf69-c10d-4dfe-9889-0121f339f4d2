import React, { useRef, useState } from 'react';
import CinemaController from './CinemaController';
import CinemaTransition, { CinemaTransitionRef } from './CinemaTransition';
import AutoCycleManager, { AutoCycleManagerRef, AutoCyclePhase } from './AutoCycleManager';

// 🧪 COMPOSANT DE TEST POUR LE SYSTÈME CINÉMATOGRAPHIQUE
// Permet de tester les volets et le cycle automatique indépendamment

const CinemaTest: React.FC = () => {
  // 🎭 Références pour les composants
  const transitionRef = useRef<CinemaTransitionRef>(null);
  const cycleManagerRef = useRef<AutoCycleManagerRef>(null);

  // 📊 États de test
  const [currentPhase, setCurrentPhase] = useState<AutoCyclePhase>('aube');
  const [progress, setProgress] = useState(0);
  const [phaseProgress, setPhaseProgress] = useState(0);
  const [isTransitionOpen, setIsTransitionOpen] = useState(false);
  const [isCycleRunning, setIsCycleRunning] = useState(false);

  // 🎬 Handlers pour les volets
  const handleOpenCurtains = async () => {
    console.log('🧪 Test: Ouverture des volets...');
    await transitionRef.current?.openCurtains();
    setIsTransitionOpen(true);
  };

  const handleCloseCurtains = async () => {
    console.log('🧪 Test: Fermeture des volets...');
    await transitionRef.current?.closeCurtains();
    setIsTransitionOpen(false);
  };

  const handleResetCurtains = () => {
    console.log('🧪 Test: Réinitialisation des volets...');
    transitionRef.current?.resetCurtains();
    setIsTransitionOpen(false);
  };

  // 🌅 Handlers pour le cycle automatique
  const handleStartCycle = () => {
    console.log('🧪 Test: Démarrage du cycle...');
    cycleManagerRef.current?.startCycle();
    setIsCycleRunning(true);
  };

  const handleStopCycle = () => {
    console.log('🧪 Test: Arrêt du cycle...');
    cycleManagerRef.current?.stopCycle();
    setIsCycleRunning(false);
  };

  const handlePauseCycle = () => {
    console.log('🧪 Test: Pause du cycle...');
    cycleManagerRef.current?.pauseCycle();
  };

  const handleResumeCycle = () => {
    console.log('🧪 Test: Reprise du cycle...');
    cycleManagerRef.current?.resumeCycle();
  };

  // 📊 Callbacks du cycle
  const handlePhaseChange = (phase: AutoCyclePhase, phaseIndex: number) => {
    console.log(`🧪 Test: Changement de phase - ${phase} (${phaseIndex + 1}/4)`);
    setCurrentPhase(phase);
  };

  const handleCycleComplete = () => {
    console.log('🧪 Test: Cycle terminé');
    setIsCycleRunning(false);
  };

  const handleProgress = (totalProgress: number, currentPhaseProgress: number) => {
    setProgress(totalProgress);
    setPhaseProgress(currentPhaseProgress);
  };

  // 🎨 Émojis des phases
  const phaseEmojis = {
    aube: '🌅',
    midi: '☀️',
    coucher: '🌇',
    nuit: '🌙'
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
      {/* 🎭 Composant de transition (volets) */}
      <CinemaTransition
        ref={transitionRef}
        onOpenComplete={() => console.log('🧪 Volets ouverts')}
        onCloseComplete={() => console.log('🧪 Volets fermés')}
      />

      {/* 🌅 Gestionnaire de cycle automatique */}
      <AutoCycleManager
        ref={cycleManagerRef}
        onPhaseChange={handlePhaseChange}
        onCycleComplete={handleCycleComplete}
        onProgress={handleProgress}
      />

      {/* 🎮 Interface de test */}
      <div className="bg-black/50 backdrop-blur-sm rounded-lg p-8 max-w-md w-full mx-4 text-white">
        <h1 className="text-2xl font-bold mb-6 text-center">
          🎬 Test Système Cinématographique
        </h1>

        {/* 📊 Informations d'état */}
        <div className="mb-6 space-y-2">
          <div className="flex justify-between">
            <span>Phase actuelle:</span>
            <span className="font-mono">
              {phaseEmojis[currentPhase]} {currentPhase}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Progrès total:</span>
            <span className="font-mono">{(progress * 100).toFixed(1)}%</span>
          </div>
          <div className="flex justify-between">
            <span>Progrès phase:</span>
            <span className="font-mono">{(phaseProgress * 100).toFixed(1)}%</span>
          </div>
          <div className="flex justify-between">
            <span>Volets:</span>
            <span className="font-mono">
              {isTransitionOpen ? '🔓 Ouverts' : '🔒 Fermés'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Cycle:</span>
            <span className="font-mono">
              {isCycleRunning ? '▶️ En cours' : '⏹️ Arrêté'}
            </span>
          </div>
        </div>

        {/* 🎭 Contrôles des volets */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">🎭 Volets</h3>
          <div className="grid grid-cols-3 gap-2">
            <button
              onClick={handleOpenCurtains}
              className="px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm transition-colors"
            >
              Ouvrir
            </button>
            <button
              onClick={handleCloseCurtains}
              className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors"
            >
              Fermer
            </button>
            <button
              onClick={handleResetCurtains}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm transition-colors"
            >
              Reset
            </button>
          </div>
        </div>

        {/* 🌅 Contrôles du cycle */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">🌅 Cycle Automatique</h3>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={handleStartCycle}
              disabled={isCycleRunning}
              className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 rounded text-sm transition-colors"
            >
              Démarrer
            </button>
            <button
              onClick={handleStopCycle}
              disabled={!isCycleRunning}
              className="px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-500 rounded text-sm transition-colors"
            >
              Arrêter
            </button>
            <button
              onClick={handlePauseCycle}
              disabled={!isCycleRunning}
              className="px-3 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-500 rounded text-sm transition-colors"
            >
              Pause
            </button>
            <button
              onClick={handleResumeCycle}
              disabled={!isCycleRunning}
              className="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 rounded text-sm transition-colors"
            >
              Reprendre
            </button>
          </div>
        </div>

        {/* 📊 Barres de progression */}
        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Progrès total</span>
              <span>{(progress * 100).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress * 100}%` }}
              />
            </div>
          </div>
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Progrès phase</span>
              <span>{(phaseProgress * 100).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${phaseProgress * 100}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CinemaTest;
